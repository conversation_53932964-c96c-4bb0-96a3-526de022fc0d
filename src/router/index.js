import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 页面组件 - 使用动态导入实现代码分割
const Home = () => import('@/views/Home.vue')
const Login = () => import('@/views/auth/Login.vue')
const Dashboard = () => import('@/views/dashboard/Index.vue')
const AgentSquare = () => import('@/views/agent/Square.vue')
const AgentDetail = () => import('@/views/agent/Detail.vue')
const CallHistory = () => import('@/views/dashboard/History.vue')
const ApiKeys = () => import('@/views/dashboard/ApiKeys.vue')
const Subscriptions = () => import('@/views/dashboard/Subscriptions.vue')
const ApiDocs = () => import('@/views/ApiDocs.vue')

// 法律页面
const Privacy = () => import('@/views/legal/Privacy.vue')
const Terms = () => import('@/views/legal/Terms.vue')
const Agreement = () => import('@/views/legal/Agreement.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: '首页' }
  },

  {
    path: '/api-docs',
    name: 'ApiDocs',
    component: ApiDocs,
    meta: { title: 'API文档' }
  },
  {
    path: '/auth/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录', guest: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { title: '仪表盘', requiresAuth: true }
  },
  {
    path: '/agent/square',
    name: 'AgentSquare',
    component: AgentSquare,
    meta: { title: 'Agent广场', requiresAuth: true }
  },
  {
    path: '/agent/detail/:id',
    name: 'AgentDetail',
    component: AgentDetail,
    meta: { title: 'Agent详情', requiresAuth: true }
  },
  {
    path: '/dashboard/history',
    name: 'CallHistory',
    component: CallHistory,
    meta: { title: '调用历史', requiresAuth: true }
  },
  {
    path: '/dashboard/subscriptions',
    name: 'Subscriptions',
    component: Subscriptions,
    meta: { title: '我的订阅', requiresAuth: true }
  },
  {
    path: '/dashboard/api-keys',
    name: 'ApiKeys',
    component: ApiKeys,
    meta: { title: 'API密钥', requiresAuth: true }
  },

  // 法律页面
  {
    path: '/legal/privacy',
    name: 'Privacy',
    component: Privacy,
    meta: { title: '隐私政策' }
  },
  {
    path: '/legal/terms',
    name: 'Terms',
    component: Terms,
    meta: { title: '服务条款' }
  },
  {
    path: '/legal/agreement',
    name: 'Agreement',
    component: Agreement,
    meta: { title: '使用协议' }
  },

  // 注释掉通配符路由，避免干扰正常路由
  // {
  //   path: '/:pathMatch(.*)*',
  //   redirect: '/'
  // }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  console.log('=== 路由守卫开始 ===')
  console.log('访问路径:', to.path)
  console.log('来源路径:', from.path)
  console.log('需要认证:', to.meta.requiresAuth)

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 智能体矩阵` : '智能体矩阵'

  // 如果不需要认证，直接通过
  if (!to.meta.requiresAuth) {
    console.log('页面不需要认证，直接通过')
    next()
    return
  }

  console.log('页面需要认证，开始检查...')
  console.log('当前认证状态:', {
    isInitialized: authStore.isInitialized,
    hasToken: !!authStore.token,
    hasUser: !!authStore.user,
    isAuthenticated: authStore.isAuthenticated
  })

  // 如果没有Token，直接跳转到登录页
  if (!authStore.token) {
    console.log('没有Token，跳转到登录页')
    next('/auth/login')
    return
  }

  // 如果有Token但未初始化，先初始化
  if (!authStore.isInitialized) {
    console.log('有Token但未初始化，开始初始化用户信息...')
    try {
      const tokenValid = await authStore.initUser()
      console.log('用户信息初始化结果:', tokenValid)
      if (!tokenValid) {
        console.log('Token验证失败，跳转到登录页')
        next('/auth/login')
        return
      }
    } catch (error) {
      console.error('初始化用户信息失败:', error)
      next('/auth/login')
      return
    }
  }

  // 最终检查认证状态
  if (!authStore.isAuthenticated) {
    console.log('用户未认证，跳转到登录页')
    next('/auth/login')
    return
  }

  console.log('认证通过，允许访问', to.path)

  // 如果是访客页面且已登录，重定向到仪表盘
  if (to.meta.guest && authStore.isAuthenticated) {
    console.log('访客页面且已登录，重定向到仪表盘')
    next('/dashboard')
    return
  }

  console.log('=== 路由守卫结束 ===')
  next()
})

export default router
