<template>
  <div class="form-test-page">
    <Header />
    
    <main class="main-content">
      <!-- 页面头部区域 -->
      <div class="page-header">
        <div class="container" style="text-align: center;">
          <h1 class="page-title">证件信息填写测试</h1>
          <p class="description" style="color: white;">体验智能表单填写功能，测试各种证件信息的识别和录入</p>
        </div>
      </div>

      <!-- 表单内容区域 -->
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-lg-8 col-md-10">
            <div class="kt-card">
              <div class="kt-card-header">
                <h3 class="kt-card-title">
                  <i class="fas fa-id-card me-2"></i>证件信息表单
                </h3>
              </div>
              
              <div class="kt-card-body">
                <form @submit.prevent="handleSubmit" class="form-test">
                  <div class="row">
                    <!-- 文件类型 -->
                    <div class="col-md-6 mb-4">
                      <label class="form-label required">文件类型</label>
                      <select 
                        v-model="formData.DOCUMENTTYPE" 
                        class="form-select"
                        :class="{ 'is-invalid': errors.DOCUMENTTYPE }"
                        required
                      >
                        <option value="">请选择文件类型</option>
                        <option value="护照">护照</option>
                        <option value="身份证">身份证</option>
                        <option value="驾驶证">驾驶证</option>
                        <option value="港澳通行证">港澳通行证</option>
                        <option value="台湾通行证">台湾通行证</option>
                      </select>
                      <div v-if="errors.DOCUMENTTYPE" class="invalid-feedback">
                        {{ errors.DOCUMENTTYPE }}
                      </div>
                    </div>

                    <!-- 文件编码 -->
                    <div class="col-md-6 mb-4">
                      <label class="form-label required">文件编码</label>
                      <input 
                        v-model="formData.DOCUMENTNUMBER" 
                        type="text" 
                        class="form-control"
                        :class="{ 'is-invalid': errors.DOCUMENTNUMBER }"
                        placeholder="请输入证件号码"
                        required
                      >
                      <div v-if="errors.DOCUMENTNUMBER" class="invalid-feedback">
                        {{ errors.DOCUMENTNUMBER }}
                      </div>
                    </div>

                    <!-- 全名 -->
                    <div class="col-md-6 mb-4">
                      <label class="form-label required">全名</label>
                      <input 
                        v-model="formData.FULLNAME" 
                        type="text" 
                        class="form-control"
                        :class="{ 'is-invalid': errors.FULLNAME }"
                        placeholder="请输入完整姓名"
                        required
                      >
                      <div v-if="errors.FULLNAME" class="invalid-feedback">
                        {{ errors.FULLNAME }}
                      </div>
                    </div>

                    <!-- 姓氏 -->
                    <div class="col-md-3 mb-4">
                      <label class="form-label">姓氏</label>
                      <input 
                        v-model="formData.SURNAME" 
                        type="text" 
                        class="form-control"
                        placeholder="姓"
                      >
                    </div>

                    <!-- 名字 -->
                    <div class="col-md-3 mb-4">
                      <label class="form-label">名字</label>
                      <input 
                        v-model="formData.GIVENNAME" 
                        type="text" 
                        class="form-control"
                        placeholder="名"
                      >
                    </div>

                    <!-- 性别 -->
                    <div class="col-md-6 mb-4">
                      <label class="form-label required">性别</label>
                      <select
                        v-model="formData.SEX"
                        class="form-select"
                        :class="{ 'is-invalid': errors.SEX }"
                        required
                      >
                        <option value="">请选择性别</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                      </select>
                      <div v-if="errors.SEX" class="invalid-feedback">
                        {{ errors.SEX }}
                      </div>
                    </div>

                    <!-- 国籍 -->
                    <div class="col-md-6 mb-4">
                      <label class="form-label required">国籍</label>
                      <input 
                        v-model="formData.NATIONALITY" 
                        type="text" 
                        class="form-control"
                        :class="{ 'is-invalid': errors.NATIONALITY }"
                        placeholder="请输入国籍"
                        required
                      >
                      <div v-if="errors.NATIONALITY" class="invalid-feedback">
                        {{ errors.NATIONALITY }}
                      </div>
                    </div>

                    <!-- 生日 -->
                    <div class="col-md-6 mb-4">
                      <label class="form-label required">生日</label>
                      <input 
                        v-model="formData.BIRTHDAY" 
                        type="date" 
                        class="form-control"
                        :class="{ 'is-invalid': errors.BIRTHDAY }"
                        required
                      >
                      <div v-if="errors.BIRTHDAY" class="invalid-feedback">
                        {{ errors.BIRTHDAY }}
                      </div>
                    </div>

                    <!-- 签发日期 -->
                    <div class="col-md-6 mb-4">
                      <label class="form-label required">签发日期</label>
                      <input 
                        v-model="formData.DATEOFISSUE" 
                        type="date" 
                        class="form-control"
                        :class="{ 'is-invalid': errors.DATEOFISSUE }"
                        required
                      >
                      <div v-if="errors.DATEOFISSUE" class="invalid-feedback">
                        {{ errors.DATEOFISSUE }}
                      </div>
                    </div>

                    <!-- 到期日期 -->
                    <div class="col-md-6 mb-4">
                      <label class="form-label required">到期日期</label>
                      <input 
                        v-model="formData.EXPIRATIONDATE" 
                        type="date" 
                        class="form-control"
                        :class="{ 'is-invalid': errors.EXPIRATIONDATE }"
                        required
                      >
                      <div v-if="errors.EXPIRATIONDATE" class="invalid-feedback">
                        {{ errors.EXPIRATIONDATE }}
                      </div>
                    </div>
                  </div>

                  <!-- 提交按钮区域 -->
                  <div class="form-actions">
                    <button 
                      type="submit" 
                      class="btn btn-gradient-primary btn-lg me-3"
                      :disabled="isSubmitting"
                    >
                      <i class="fas fa-check me-2"></i>
                      {{ isSubmitting ? '提交中...' : '提交表单' }}
                    </button>
                    
                    <button 
                      type="button" 
                      class="btn btn-secondary btn-lg me-3"
                      @click="resetForm"
                    >
                      <i class="fas fa-undo me-2"></i>重置表单
                    </button>

                    <button 
                      type="button" 
                      class="btn btn-info btn-lg"
                      @click="fillSampleData"
                    >
                      <i class="fas fa-magic me-2"></i>填充示例数据
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <!-- 提交结果显示 -->
            <div v-if="submitResult" class="kt-card mt-4">
              <div class="kt-card-header">
                <h3 class="kt-card-title">
                  <i class="fas fa-clipboard-check me-2"></i>提交结果
                </h3>
              </div>
              <div class="kt-card-body">
                <div class="alert alert-success">
                  <h5><i class="fas fa-check-circle me-2"></i>表单提交成功！</h5>
                  <p class="mb-0">以下是您提交的信息：</p>
                </div>
                
                <div class="result-data">
                  <div class="row">
                    <div v-for="(value, key) in submitResult" :key="key" class="col-md-6 mb-3">
                      <div class="result-item">
                        <label class="result-label">{{ getFieldLabel(key) }}：</label>
                        <span class="result-value">{{ value || '未填写' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <Footer />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useToast } from 'vue-toastification'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

const toast = useToast()

// 表单数据
const formData = reactive({
  SEX: '',
  SURNAME: '',
  BIRTHDAY: '',
  FULLNAME: '',
  GIVENNAME: '',
  DATEOFISSUE: '',
  NATIONALITY: '',
  DOCUMENTTYPE: '',
  DOCUMENTNUMBER: '',
  EXPIRATIONDATE: ''
})

// 表单验证错误
const errors = reactive({})

// 提交状态和结果
const isSubmitting = ref(false)
const submitResult = ref(null)

// 字段标签映射
const fieldLabels = {
  SEX: '性别',
  SURNAME: '姓氏',
  BIRTHDAY: '生日',
  FULLNAME: '全名',
  GIVENNAME: '名字',
  DATEOFISSUE: '签发日期',
  NATIONALITY: '国籍',
  DOCUMENTTYPE: '文件类型',
  DOCUMENTNUMBER: '文件编码',
  EXPIRATIONDATE: '到期日期'
}

// 获取字段标签
const getFieldLabel = (key) => {
  return fieldLabels[key] || key
}

// 表单验证
const validateForm = () => {
  const newErrors = {}
  
  // 必填字段验证
  const requiredFields = ['DOCUMENTTYPE', 'DOCUMENTNUMBER', 'FULLNAME', 'SEX', 'NATIONALITY', 'BIRTHDAY', 'DATEOFISSUE', 'EXPIRATIONDATE']
  
  requiredFields.forEach(field => {
    if (!formData[field] || formData[field].trim() === '') {
      newErrors[field] = `${fieldLabels[field]}不能为空`
    }
  })

  // 日期验证
  if (formData.BIRTHDAY && formData.DATEOFISSUE) {
    const birthday = new Date(formData.BIRTHDAY)
    const issueDate = new Date(formData.DATEOFISSUE)
    
    if (issueDate < birthday) {
      newErrors.DATEOFISSUE = '签发日期不能早于生日'
    }
  }

  if (formData.DATEOFISSUE && formData.EXPIRATIONDATE) {
    const issueDate = new Date(formData.DATEOFISSUE)
    const expirationDate = new Date(formData.EXPIRATIONDATE)
    
    if (expirationDate <= issueDate) {
      newErrors.EXPIRATIONDATE = '到期日期必须晚于签发日期'
    }
  }

  // 清空之前的错误并设置新错误
  Object.keys(errors).forEach(key => delete errors[key])
  Object.assign(errors, newErrors)
  
  return Object.keys(newErrors).length === 0
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    toast.error('请检查表单填写是否正确')
    return
  }

  isSubmitting.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 设置提交结果
    submitResult.value = { ...formData }
    
    toast.success('表单提交成功！')
    
    // 滚动到结果区域
    setTimeout(() => {
      const resultElement = document.querySelector('.result-data')
      if (resultElement) {
        resultElement.scrollIntoView({ behavior: 'smooth' })
      }
    }, 100)
    
  } catch (error) {
    console.error('提交失败:', error)
    toast.error('提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  Object.keys(errors).forEach(key => delete errors[key])
  submitResult.value = null
  toast.info('表单已重置')
}

// 填充示例数据
const fillSampleData = () => {
  Object.assign(formData, {
    SEX: '男',
    SURNAME: '张',
    BIRTHDAY: '1990-05-15',
    FULLNAME: '张三',
    GIVENNAME: '三',
    DATEOFISSUE: '2020-01-10',
    NATIONALITY: '中国',
    DOCUMENTTYPE: '身份证',
    DOCUMENTNUMBER: '110101199005151234',
    EXPIRATIONDATE: '2030-01-10'
  })
  
  // 清空错误
  Object.keys(errors).forEach(key => delete errors[key])
  submitResult.value = null
  
  toast.success('示例数据已填充')
}
</script>

<style scoped>
/* 表单测试页面样式 */
.form-test-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 140px);
}

/* 页面头部样式 */
.page-header {
  background: var(--kt-gradient-primary);
  padding: 3rem 0 2rem;
  margin-bottom: 2rem;
}

.page-title {
  color: white;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.description {
  font-size: 1.125rem;
  margin-bottom: 0;
  opacity: 0.9;
}

/* 表单样式 */
.form-test {
  padding: 0;
}

.form-label {
  font-weight: 600;
  color: var(--kt-text-dark);
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: ' *';
  color: var(--kt-danger);
}

.form-control,
.form-select {
  border-radius: var(--kt-border-radius);
  border: 1px solid #e4e6ea;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
  border-color: var(--kt-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
}

.form-control.is-invalid,
.form-select.is-invalid {
  border-color: var(--kt-danger);
}

.invalid-feedback {
  color: var(--kt-danger);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}



/* 表单操作按钮样式 */
.form-actions {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e4e6ea;
  text-align: center;
}

.btn-lg {
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 结果显示样式 */
.result-data {
  margin-top: 1rem;
}

.result-item {
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: var(--kt-border-radius-sm);
  border-left: 3px solid var(--kt-primary);
}

.result-label {
  font-weight: 600;
  color: var(--kt-text-dark);
  display: block;
  margin-bottom: 0.25rem;
}

.result-value {
  color: var(--kt-text-gray-700);
  font-size: 0.95rem;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .form-actions {
    text-align: center;
  }

  .form-actions .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

/* 动画效果 */
.kt-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单验证动画 */
.form-control.is-invalid,
.form-select.is-invalid {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}
</style>
