<template>
  <div class="agent-square-page">
    <Header />
    
    <main class="main-content">
      <!-- 页面头部区域 -->
      <div class="page-header">
        <div class="container" style="text-align: center;">
          <p class="description" style="color: white;">探索智能Agent世界，发现AI助手的无限可能</p>

          <!-- 搜索区域 -->
          <div class="search-section">
            <form @submit.prevent="handleSearch">
              <div class="input-group">
                <input
                  v-model="searchForm.keyword"
                  type="text"
                  class="form-control"
                  placeholder="搜索Agent名称、功能描述或应用场景..."
                >
                <button class="btn btn-primary" type="submit">
                  <i class="fas fa-search me-2"></i>搜索Agent
                </button>
              </div>
            </form>
          </div>

          <!-- 体验按钮区域 -->
          <div class="experience-section">
            <router-link
              to="/test/form"
              class="btn btn-gradient-success btn-lg experience-btn"
            >
              <i class="fas fa-magic me-2"></i>体验一下
            </router-link>
            <p class="experience-desc">试试我们的智能表单填写功能</p>
          </div>
        </div>
      </div>

      <div class="container">
        <!-- 分类筛选区域 -->
        <div class="category-filters">
          <div class="d-flex flex-wrap">
            <button
              class="btn"
              :class="{ active: !searchForm.category }"
              @click="filterByCategory('')"
            >
              <i class="fas fa-th-large me-2"></i>全部Agent
            </button>
            <button
              v-for="category in categories"
              :key="category.id"
              class="btn"
              :class="{ active: searchForm.category === category.id }"
              @click="filterByCategory(category.id)"
            >
              <i class="fas fa-tag me-2"></i>
              {{ category.categoryName }}
            </button>
          </div>
        </div>

        <!-- Agent卡片列表 -->
        <div v-if="agents.length > 0" class="row agent-cards-row g-4">
          <div
            v-for="agent in agents"
            :key="agent.id"
            class="col-lg-4 col-md-4 col-sm-12 mb-4"
          >
            <div class="agent-card">
              <!-- 打分统计 -->
              <div class="rating-stats">
                <div class="rating-item good">
                  <span class="rating-icon">▲</span>
                  <span class="rating-count">{{ agent.goodRating || 0 }}</span>
                </div>
                <div class="rating-item bad">
                  <span class="rating-icon">▼</span>
                  <span class="rating-count">{{ agent.badRating || 0 }}</span>
                </div>
              </div>

              <!-- 卡片头部 -->
              <div class="agent-card-header">
                <div class="agent-icon">
                  {{ getAgentIcon(agent.displayName) }}
                </div>
                <div class="agent-info">
                  <h3 class="agent-title">{{ agent.displayName }}</h3>
                  <div class="agent-meta">
                    <span class="agent-category">{{ agent.categoryText || '未分类' }}</span>
                    <span class="agent-version">v{{ agent.currentVersionNumber || agent.version || '1.0.0' }}</span>
                    <span class="agent-subscription">
                      <i class="fas fa-users me-1"></i>
                      <span class="subscription-count">{{ agent.subscriptionCount || 0 }}</span>
                    </span>
                  </div>
                </div>
              </div>

              <!-- 卡片主体 -->
              <div class="agent-card-body">
                <div class="agent-description">
                  {{ agent.description || 'Agent功能描述信息，介绍该Agent的主要功能和使用场景，帮助用户快速了解Agent特点。' }}
                </div>
              </div>

              <!-- 卡片底部 -->
              <div class="agent-card-footer">
                <div class="agent-actions">
                  <router-link
                    :to="`/agent/detail/${agent.id}`"
                    class="action-btn btn-detail"
                  >
                    <i class="fas fa-eye me-1"></i>
                    <span>查看详情</span>
                  </router-link>
                  <button
                    class="action-btn btn-subscribe"
                    :class="{ subscribed: agent.isSubscribed }"
                    :data-agent-id="agent.id"
                    :data-agent-name="agent.displayName"
                    :title="agent.isSubscribed ? '点击取消订阅' : '点击订阅此Agent'"
                    @click="toggleSubscription(agent)"
                    :disabled="agent.isSubscribing"
                  >
                    <i :class="agent.isSubscribed ? 'fas fa-heart me-1' : 'far fa-heart me-1'"></i>
                    <span class="subscribe-text">{{ agent.isSubscribed ? '已订阅' : '订阅' }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state text-center py-5">
          <i class="fas fa-robot fa-4x text-muted mb-3"></i>
          <h5>暂未找到匹配的Agent</h5>
          <p class="text-muted">很抱歉，没有找到符合条件的智能Agent。您可以尝试调整搜索关键词或选择其他分类。</p>
          <div class="d-flex gap-3 justify-content-center">
            <button class="btn btn-primary" @click="resetSearch">
              <i class="fas fa-th-large me-2"></i>浏览全部Agent
            </button>
            <button class="btn btn-outline-primary" @click="focusSearchInput">
              <i class="fas fa-search me-2"></i>重新搜索
            </button>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.pages > 1" class="d-flex justify-content-center mt-4">
          <nav>
            <ul class="pagination">
              <li class="page-item" :class="{ disabled: pagination.current === 1 }">
                <button
                  class="page-link"
                  @click="changePage(pagination.current - 1)"
                  :disabled="pagination.current === 1"
                >
                  上一页
                </button>
              </li>

              <li
                v-for="page in visiblePages"
                :key="page"
                class="page-item"
                :class="{ active: page === pagination.current }"
              >
                <button
                  v-if="typeof page === 'number'"
                  class="page-link"
                  @click="changePage(page)"
                >
                  {{ page }}
                </button>
                <span v-else class="page-link">{{ page }}</span>
              </li>

              <li class="page-item" :class="{ disabled: pagination.current === pagination.pages }">
                <button
                  class="page-link"
                  @click="changePage(pagination.current + 1)"
                  :disabled="pagination.current === pagination.pages"
                >
                  下一页
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </main>

    <Footer />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import api from '@/utils/api'

const route = useRoute()
const router = useRouter()
const toast = useToast()

// 响应式数据
const searchForm = reactive({
  keyword: route.query.keyword || '',
  category: route.query.category || ''
})

const agents = ref([])
const categories = ref([])
const loading = ref(false)
const pagination = reactive({
  current: 1,
  pages: 1,
  total: 0,
  size: 12
})

// 计算属性
const visiblePages = computed(() => {
  const pages = []
  const total = pagination.pages
  const current = pagination.current

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }

  return pages
})

// 方法
const getAgentIcon = (displayName) => {
  return displayName ? displayName.charAt(0).toUpperCase() : 'A'
}

const loadAgents = async (params = {}) => {
  try {
    loading.value = true

    // 构建查询参数
    const queryParams = {
      page: params.page || pagination.current,
      size: pagination.size,
      ...(searchForm.category && { category: searchForm.category }),
      ...(searchForm.keyword && { keyword: searchForm.keyword })
    }

    const response = await api.get('/api/v1/agent/list', { params: queryParams })
    const data = response.data

    if (data.success) {
      agents.value = data.data || []

      // 更新分页信息
      if (data.pagination) {
        pagination.current = data.pagination.current
        pagination.total = data.pagination.total
        pagination.pages = data.pagination.pages
      }
    } else {
      toast.error(data.message || '加载Agent列表失败')
    }

  } catch (error) {
    console.error('加载Agent列表失败:', error)
    toast.error('加载Agent列表失败，请刷新页面重试')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await api.get('/api/v1/agent/categories')
    const data = response.data

    if (data.success) {
      categories.value = data.data || []
    } else {
      console.error('加载分类失败:', data.message)
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadAgents({ page: 1 })
  updateURL()
}

const filterByCategory = (categoryId) => {
  searchForm.category = categoryId
  handleSearch()
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.category = ''
  handleSearch()
}

const focusSearchInput = () => {
  nextTick(() => {
    const searchInput = document.querySelector('input[type="text"]')
    if (searchInput) {
      searchInput.focus()
    }
  })
}

const changePage = (page) => {
  if (page >= 1 && page <= pagination.pages) {
    pagination.current = page
    loadAgents({ page })
    updateURL({ page })
  }
}

const updateURL = (params = {}) => {
  const query = {
    ...route.query,
    keyword: searchForm.keyword || undefined,
    category: searchForm.category || undefined,
    page: params.page || pagination.current || 1
  }

  // 移除空值
  Object.keys(query).forEach(key => {
    if (!query[key] || query[key] === '1') {
      delete query[key]
    }
  })

  router.replace({ query })
}

const toggleSubscription = async (agent) => {
  if (agent.isSubscribing) return

  agent.isSubscribing = true
  const originalIsSubscribed = agent.isSubscribed
  const originalCount = agent.subscriptionCount

  try {
    const response = await api.post(`/api/v1/agent/${agent.id}/toggle-subscription`)
    const data = response.data

    if (data.success) {
      const action = data.isSubscribed ? '订阅' : '取消订阅'
      toast.success(`${action}成功！`)

      // 更新本地状态
      agent.isSubscribed = data.isSubscribed
      agent.subscriptionCount = data.subscriptionCount || (data.isSubscribed ? originalCount + 1 : Math.max(0, originalCount - 1))
    } else {
      toast.error(data.message || '操作失败，请重试')
      // 恢复原始状态
      agent.isSubscribed = originalIsSubscribed
      agent.subscriptionCount = originalCount
    }
  } catch (error) {
    console.error('订阅操作失败:', error)
    toast.error('网络错误，请检查网络连接后重试')
    // 恢复原始状态
    agent.isSubscribed = originalIsSubscribed
    agent.subscriptionCount = originalCount
  } finally {
    agent.isSubscribing = false
  }
}




// 生命周期
onMounted(async () => {
  await loadCategories()
  await loadAgents({ page: route.query.page || 1 })
})
</script>

<style scoped>
/* Agent广场页面样式 - 与HTML版本保持一致 */
.agent-square-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 140px); /* 减去Header和Footer的大概高度 */
}



/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #009ef7 0%, #7239ea 100%);
  padding: 3rem 0 2rem;
  margin-bottom: 2rem;
}

.description {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* 搜索区域样式 */
.search-section {
  max-width: 600px;
  margin: 0 auto;
}

.search-section .input-group {
  box-shadow: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
  border-radius: 0.625rem;
  overflow: hidden;
}

.search-section .form-control {
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  background: white;
}

.search-section .btn {
  padding: 1rem 2rem;
  border: none;
  background: #009ef7;
  color: white;
  font-weight: 600;
  transition: all 0.2s ease;
}

/* 体验按钮区域样式 */
.experience-section {
  margin-top: 2rem;
  text-align: center;
}

.experience-btn {
  padding: 0.875rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(80, 205, 137, 0.3);
}

.experience-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(80, 205, 137, 0.4);
  text-decoration: none;
}

.experience-desc {
  margin-top: 0.75rem;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0;
}

/* 分类筛选区域 */
.category-filters {
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background: #ffffff !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #e2e8f0 !important;
}

.category-filters .btn {
  margin: 0.25rem 0.5rem 0.25rem 0;
  padding: 0.5rem 1rem;
  border: 1px solid #eff2f5;
  background: #ffffff;
  color: #64748b;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 400;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
}

.category-filters .btn:hover {
  color: #009ef7;
  border-color: #009ef7;
  background: #f1faff;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
}

.category-filters .btn.active {
  color: white;
  background: #009ef7;
  border-color: #009ef7;
  box-shadow: none;
  font-weight: 600;
}
/* Agent卡片样式 - 与HTML版本完全一致 */
.agent-cards-row {
  margin-bottom: 3rem;
}

.agent-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 0.625rem;
  box-shadow: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-bottom: 2.5rem;
  position: relative;
}

.agent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 2rem 0.5rem rgba(0, 0, 0, 0.1);
  border-color: #009ef7;
}

/* 打分统计 */
.rating-stats {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  z-index: 2;
}

.rating-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.rating-item.good {
  color: #059669;
}

.rating-item.bad {
  color: #dc2626;
}

.rating-icon {
  font-size: 0.875rem;
  font-weight: 700;
}

/* 卡片头部 */
.agent-card-header {
  padding: 1.25rem 1.25rem 0.75rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

/* Agent图标 */
.agent-icon {
  width: 50px;
  height: 50px;
  border-radius: 0.375rem;
  background: #009ef7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.875rem;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 2px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
  font-family: var(--kt-font-sans-serif, 'Inter', sans-serif);
}

/* Agent信息区域 */
.agent-info {
  flex: 1;
  min-width: 0;
}

.agent-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.375rem 0;
  line-height: 1.3;
}

/* 卡片主体 */
.agent-card-body {
  flex: 1 1 auto;
  padding: 0 1.25rem 0.75rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.agent-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.agent-category {
  font-size: 0.75rem;
  color: #64748b;
  background: #f5f8fa;
  padding: 0.25rem 0.5rem;
  border-radius: 0.475rem;
  border: 1px solid #eff2f5;
  font-weight: 400;
}

.agent-version {
  font-size: 0.75rem;
  color: #009ef7;
  background: #f1faff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.475rem;
  font-weight: 600;
  border: 1px solid rgba(0, 158, 247, 0.2);
}

.agent-subscription {
  font-size: 0.75rem;
  color: #6f42c1;
  background: #f8f5ff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.475rem;
  font-weight: 600;
  border: 1px solid rgba(111, 66, 193, 0.2);
  display: inline-flex;
  align-items: center;
}

.agent-subscription i {
  font-size: 0.7rem;
}

.agent-description {
  color: #475569;
  font-size: 0.8125rem;
  line-height: 1.5;
  margin-bottom: 0.75rem;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  max-height: 3.75rem;
}
/* 卡片底部 */
.agent-card-footer {
  padding: 0.75rem 1.25rem 1.25rem;
  background-color: transparent;
  border-top: 1px solid #eff2f5;
  margin-top: auto;
  flex-shrink: 0;
}

.agent-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-top: 1rem;
}

.action-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.5;
  color: #64748b;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  background-color: #ffffff;
  border: 1px solid #eff2f5;
  border-radius: 0.475rem;
  transition: all 0.2s ease;
  flex: 1;
  overflow: hidden;
}

.action-btn:hover {
  color: #009ef7;
  border-color: #009ef7;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
}

/* 订阅按钮特定样式 */
.btn-subscribe {
  color: #64748b;
  background: #ffffff;
  border-color: #eff2f5;
}

.btn-subscribe:hover {
  background: #f1faff;
  border-color: #009ef7;
  color: #009ef7;
}

/* 已订阅状态 */
.btn-subscribe.subscribed {
  background: #009ef7;
  border-color: #009ef7;
  color: white;
}

.btn-subscribe.subscribed:hover {
  background: #0084d1;
  border-color: #0084d1;
  color: white;
}

.btn-subscribe.subscribed i {
  color: white;
}

/* 按钮禁用状态 */
.btn-subscribe:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-subscribe:disabled:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
  transform: none;
}

.btn-subscribe.subscribed:disabled:hover {
  background: #009ef7;
  border-color: #009ef7;
  color: white;
  transform: none;
}

/* 分页样式 */
.pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.375rem;
  --bs-pagination-font-size: 0.875rem;
  --bs-pagination-color: #6c757d;
  --bs-pagination-bg: #fff;
  --bs-pagination-border-width: 1px;
  --bs-pagination-border-color: #dee2e6;
  --bs-pagination-border-radius: 0.375rem;
  --bs-pagination-hover-color: #495057;
  --bs-pagination-hover-bg: #e9ecef;
  --bs-pagination-hover-border-color: #adb5bd;
  --bs-pagination-focus-color: #495057;
  --bs-pagination-focus-bg: #e9ecef;
  --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(0, 158, 247, 0.25);
  --bs-pagination-active-color: #fff;
  --bs-pagination-active-bg: #009EF7;
  --bs-pagination-active-border-color: #009EF7;
  --bs-pagination-disabled-color: #adb5bd;
  --bs-pagination-disabled-bg: #fff;
  --bs-pagination-disabled-border-color: #dee2e6;
}

.pagination .page-link {
  margin: 0 2px;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease;
  font-weight: 500;
}

.pagination .page-item:first-child .page-link,
.pagination .page-item:last-child .page-link {
  border-radius: 0.375rem !important;
}

.pagination .page-item.active .page-link {
  box-shadow: 0 2px 4px rgba(0, 158, 247, 0.2);
}

/* 空状态样式 */
.empty-state {
  padding: 3rem 1rem;
  background: #ffffff;
  border-radius: 0.625rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
}

.empty-state i {
  opacity: 0.6;
}

.empty-state h5 {
  color: #64748b;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: #94a3b8;
  margin-bottom: 1.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-header {
    padding: 2rem 0 1.5rem;
  }

  .search-section .form-control {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .search-section .btn {
    padding: 0.75rem 1.5rem;
  }

  .category-filters {
    padding: 1rem;
    margin-bottom: 2rem;
  }

  .agent-cards-row {
    margin-bottom: 2rem;
  }

  .agent-card {
    margin-bottom: 2rem;
  }

  .category-filters .btn {
    margin: 0.25rem 0.25rem 0.25rem 0;
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }

  .agent-card-header {
    padding: 1rem 1rem 0.5rem;
  }

  .agent-icon {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }

  .agent-title {
    font-size: 0.9375rem;
  }

  .agent-meta {
    gap: 0.375rem;
  }

  .agent-category,
  .agent-version,
  .agent-subscription {
    font-size: 0.6875rem;
    padding: 0.1875rem 0.375rem;
  }

  .agent-description {
    font-size: 0.78125rem;
  }

  .action-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }

  .rating-stats {
    top: 0.5rem;
    right: 0.5rem;
  }

  .rating-item {
    padding: 0.1875rem 0.375rem;
    font-size: 0.6875rem;
  }
}
</style>
