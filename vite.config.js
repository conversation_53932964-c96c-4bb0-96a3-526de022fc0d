import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  // 生成构建时间戳 - 确保整个构建过程使用相同的时间戳
  const buildTime = Date.now()
  const buildTimestamp = new Date(buildTime).toISOString().replace(/[:.]/g, '-').slice(0, -5)
  const shortTimestamp = buildTime.toString().slice(-8) // 取最后8位数字

  // 输出构建信息
  if (command === 'build') {
    console.log(`🏗️  构建模式: ${mode}`)
    console.log(`📦 构建时间戳: ${buildTimestamp}`)
    console.log(`🔖 文件后缀: ${shortTimestamp}`)
  }

  return {
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: parseInt(env.VITE_DEV_PORT) || 8001,
    host: env.VITE_DEV_HOST || 'localhost',
    proxy: env.VITE_PROXY_ENABLED === 'true' ? {
      '/api': {
        target: env.VITE_PROXY_TARGET || 'http://localhost:8088',
        changeOrigin: true,
        secure: false,
        // 不需要rewrite，保持/api路径
        configure: (proxy, options) => {
          console.log(`🔗 API代理配置: ${options.target}`)
        }
      }
      // 移除 /auth 代理，让Vue前端处理 /auth/login 路由
      // 后端的认证API都在 /api/auth 下，已经被上面的 /api 代理覆盖
      // 所有Vue前端API都统一使用 /api/** 路径
    } : {}
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: env.VITE_BUILD_SOURCEMAP === 'true',
    minify: env.VITE_BUILD_MINIFY !== 'false',
    // 构建优化
    target: 'es2015',
    cssTarget: 'chrome80',
    chunkSizeWarningLimit: 1000,
    reportCompressedSize: false, // 禁用压缩大小报告以提高构建性能
    rollupOptions: {
      output: {
        // 添加时间戳后缀防止缓存 - 使用固定时间戳确保一致性
        entryFileNames: `assets/js/[name]-${shortTimestamp}.js`,
        chunkFileNames: `assets/js/[name]-${shortTimestamp}.js`,
        assetFileNames: (assetInfo) => {
          const ext = assetInfo.name.split('.').pop()
          if (/^(css)$/.test(ext)) {
            return `assets/css/[name]-${shortTimestamp}.[ext]`
          }
          if (/^(png|jpe?g|gif|svg|ico|webp)$/.test(ext)) {
            return `assets/images/[name]-${shortTimestamp}.[ext]`
          }
          if (/^(woff2?|eot|ttf|otf)$/.test(ext)) {
            return `assets/fonts/[name]-${shortTimestamp}.[ext]`
          }
          return `assets/[name]-${shortTimestamp}.[ext]`
        },
        // 手动分包配置
        manualChunks: (id) => {
          // 第三方库分包
          if (id.includes('node_modules')) {
            if (id.includes('vue') || id.includes('vue-router') || id.includes('pinia')) {
              return 'vendor-vue'
            }
            if (id.includes('bootstrap') || id.includes('@fortawesome') || id.includes('sweetalert2')) {
              return 'vendor-ui'
            }
            if (id.includes('axios') || id.includes('vue-toastification')) {
              return 'vendor-utils'
            }
            return 'vendor-other'
          }

          // 页面组件分包 - 每个页面组件打包成独立文件
          if (id.includes('/views/')) {
            // 首页
            if (id.includes('/views/Home.vue')) {
              return 'page-home'
            }
            // 认证相关
            if (id.includes('/views/auth/')) {
              return 'page-auth'
            }
            // 仪表盘相关
            if (id.includes('/views/dashboard/Index.vue')) {
              return 'page-dashboard'
            }
            if (id.includes('/views/dashboard/History.vue')) {
              return 'page-history'
            }
            if (id.includes('/views/dashboard/ApiKeys.vue')) {
              return 'page-apikeys'
            }
            if (id.includes('/views/dashboard/Subscriptions.vue')) {
              return 'page-subscriptions'
            }
            // Agent相关
            if (id.includes('/views/agent/Square.vue')) {
              return 'page-agent-square'
            }
            if (id.includes('/views/agent/Detail.vue')) {
              return 'page-agent-detail'
            }
            // API文档
            if (id.includes('/views/ApiDocs.vue')) {
              return 'page-api-docs'
            }
            // 法律页面
            if (id.includes('/views/legal/')) {
              return 'page-legal'
            }
            // 其他页面组件
            return 'page-other'
          }

          // 组件分包
          if (id.includes('/components/')) {
            if (id.includes('/components/layout/')) {
              return 'components-layout'
            }
            if (id.includes('/components/common/')) {
              return 'components-common'
            }
            return 'components-other'
          }

          // Store分包
          if (id.includes('/stores/')) {
            return 'stores'
          }

          // 工具函数分包
          if (id.includes('/utils/')) {
            return 'utils'
          }
        }
      }
    }
  },

  // 环境变量配置
  define: {
    __APP_ENV__: JSON.stringify(env.VITE_APP_ENV),
    __APP_TITLE__: JSON.stringify(env.VITE_APP_TITLE),
    __API_BASE_URL__: JSON.stringify(env.VITE_API_BASE_URL),
    __DEBUG__: JSON.stringify(env.VITE_DEBUG === 'true')
  }

  }
})
