#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * 构建报告生成器
 * 分析构建产物，生成详细的构建报告
 */
class BuildReporter {
  constructor() {
    this.distDir = path.join(__dirname, 'dist')
    this.assetsDir = path.join(this.distDir, 'assets')
    this.jsDir = path.join(this.assetsDir, 'js')
    this.cssDir = path.join(this.assetsDir, 'css')
  }

  /**
   * 获取文件大小（KB）
   */
  getFileSize(filePath) {
    try {
      const stats = fs.statSync(filePath)
      return (stats.size / 1024).toFixed(2)
    } catch (error) {
      return '0.00'
    }
  }

  /**
   * 获取目录下的所有文件
   */
  getFiles(dir, extension = '') {
    try {
      const files = fs.readdirSync(dir)
      return files
        .filter(file => extension ? file.endsWith(extension) : true)
        .map(file => ({
          name: file,
          path: path.join(dir, file),
          size: this.getFileSize(path.join(dir, file))
        }))
        .sort((a, b) => parseFloat(b.size) - parseFloat(a.size))
    } catch (error) {
      return []
    }
  }

  /**
   * 分析页面组件分包情况
   */
  analyzePageChunks() {
    const jsFiles = this.getFiles(this.jsDir, '.js')
    const pageFiles = jsFiles.filter(file => file.name.startsWith('page-'))
    
    console.log('\n📦 页面组件分包分析:')
    console.log('=' .repeat(60))
    
    const pageMapping = {
      'page-home': '首页',
      'page-auth': '登录页面',
      'page-dashboard': '仪表盘',
      'page-history': '调用历史',
      'page-apikeys': 'API密钥管理',
      'page-subscriptions': '订阅管理',
      'page-agent-square': 'Agent广场',
      'page-agent-detail': 'Agent详情',
      'page-api-docs': 'API文档',
      'page-legal': '法律页面'
    }

    pageFiles.forEach(file => {
      const baseName = file.name.replace(/-\d+\.js$/, '')
      const description = pageMapping[baseName] || baseName
      console.log(`${description.padEnd(15)} | ${file.name.padEnd(35)} | ${file.size.padStart(8)} KB`)
    })

    return pageFiles
  }

  /**
   * 分析第三方库分包情况
   */
  analyzeVendorChunks() {
    const jsFiles = this.getFiles(this.jsDir, '.js')
    const vendorFiles = jsFiles.filter(file => file.name.startsWith('vendor-'))
    
    console.log('\n📚 第三方库分包分析:')
    console.log('=' .repeat(60))
    
    const vendorMapping = {
      'vendor-vue': 'Vue核心库 (vue, vue-router, pinia)',
      'vendor-ui': 'UI库 (bootstrap, fontawesome, sweetalert2)',
      'vendor-utils': '工具库 (axios, vue-toastification)',
      'vendor-other': '其他第三方库'
    }

    vendorFiles.forEach(file => {
      const baseName = file.name.replace(/-\d+\.js$/, '')
      const description = vendorMapping[baseName] || baseName
      console.log(`${description.padEnd(40)} | ${file.name.padEnd(25)} | ${file.size.padStart(8)} KB`)
    })

    return vendorFiles
  }

  /**
   * 分析CSS文件
   */
  analyzeCSSFiles() {
    const cssFiles = this.getFiles(this.cssDir, '.css')
    
    console.log('\n🎨 CSS文件分析:')
    console.log('=' .repeat(60))
    
    cssFiles.forEach(file => {
      const type = file.name.startsWith('page-') ? '页面样式' : 
                   file.name.startsWith('vendor-') ? '第三方样式' :
                   file.name.startsWith('components-') ? '组件样式' : '其他样式'
      console.log(`${type.padEnd(15)} | ${file.name.padEnd(35)} | ${file.size.padStart(8)} KB`)
    })

    return cssFiles
  }

  /**
   * 生成构建摘要
   */
  generateSummary() {
    const jsFiles = this.getFiles(this.jsDir, '.js')
    const cssFiles = this.getFiles(this.cssDir, '.css')
    
    const totalJSSize = jsFiles.reduce((sum, file) => sum + parseFloat(file.size), 0)
    const totalCSSSize = cssFiles.reduce((sum, file) => sum + parseFloat(file.size), 0)
    
    console.log('\n📊 构建摘要:')
    console.log('=' .repeat(60))
    console.log(`JS文件数量:     ${jsFiles.length} 个`)
    console.log(`CSS文件数量:    ${cssFiles.length} 个`)
    console.log(`JS总大小:       ${totalJSSize.toFixed(2)} KB`)
    console.log(`CSS总大小:      ${totalCSSSize.toFixed(2)} KB`)
    console.log(`总大小:         ${(totalJSSize + totalCSSSize).toFixed(2)} KB`)
    
    // 分析最大的文件
    const largestJS = jsFiles[0]
    const largestCSS = cssFiles[0]
    
    if (largestJS) {
      console.log(`最大JS文件:     ${largestJS.name} (${largestJS.size} KB)`)
    }
    if (largestCSS) {
      console.log(`最大CSS文件:    ${largestCSS.name} (${largestCSS.size} KB)`)
    }
  }

  /**
   * 检查时间戳一致性
   */
  checkTimestampConsistency() {
    const jsFiles = this.getFiles(this.jsDir, '.js')
    const cssFiles = this.getFiles(this.cssDir, '.css')
    
    const allFiles = [...jsFiles, ...cssFiles]
    const timestamps = new Set()
    
    allFiles.forEach(file => {
      const match = file.name.match(/-(\d+)\.(js|css)$/)
      if (match) {
        timestamps.add(match[1])
      }
    })
    
    console.log('\n🕐 时间戳一致性检查:')
    console.log('=' .repeat(60))
    console.log(`发现时间戳:     ${Array.from(timestamps).join(', ')}`)
    console.log(`时间戳一致:     ${timestamps.size === 1 ? '✅ 是' : '❌ 否'}`)
    
    if (timestamps.size === 1) {
      const timestamp = Array.from(timestamps)[0]
      const date = new Date(parseInt(timestamp.padEnd(13, '0')))
      console.log(`构建时间:       ${date.toLocaleString('zh-CN')}`)
    }
  }

  /**
   * 生成完整报告
   */
  generateReport() {
    console.log('\n🚀 构建报告')
    console.log('=' .repeat(60))
    console.log(`生成时间: ${new Date().toLocaleString('zh-CN')}`)
    console.log(`构建目录: ${this.distDir}`)
    
    this.analyzePageChunks()
    this.analyzeVendorChunks()
    this.analyzeCSSFiles()
    this.generateSummary()
    this.checkTimestampConsistency()
    
    console.log('\n✨ 构建报告生成完成!')
    console.log('=' .repeat(60))
  }
}

// 运行报告生成器
const reporter = new BuildReporter()
reporter.generateReport()
